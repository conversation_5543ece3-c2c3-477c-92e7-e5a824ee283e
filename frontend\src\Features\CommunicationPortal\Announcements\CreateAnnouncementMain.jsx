import React, { useState, useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { ArrowLeft, Upload, X } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import AnnouncementPreview from './components/AnnouncementPreview';
import PriorityDropdown from './components/PriorityDropdown';
import LabelSelector from './components/LabelSelector';
import Calendar from './components/Calendar';
import TimePicker from './components/TimePicker';
import TowerSelector from './components/TowerSelector';
import UnitSelector from './components/UnitSelector';
import MemberSelector from './components/MemberSelector';
import GroupSelector from './components/GroupSelector';
import MessageBox from '../../../Components/MessageBox/MessageBox';
import ErrorMessage from '../../../Components/MessageBox/ErrorMessage';

// Validation schema
const announcementSchema = yup.object().shape({
  title: yup
    .string()
    .required('Title is required')
    .test('word-count', 'Title must be 10 words or less', (value) => {
      if (!value) return true;
      return value.trim().split(/\s+/).length <= 10;
    }),
  description: yup
    .string()
    .test('word-count', 'Description must be 100 words or less', (value) => {
      if (!value) return true;
      return value.trim().split(/\s+/).length <= 100;
    }),
  postAs: yup.string().required('Post as selection is required'),
  creatorName: yup.string().required('Creator name is required'),
  selectedMemberId: yup.string().when('postAs', {
    is: 'Member',
    then: (schema) => schema.required('Please select a member'),
    otherwise: (schema) => schema.notRequired()
  }),
  selectedMemberName: yup.string().when('postAs', {
    is: 'Member',
    then: (schema) => schema.required('Please select a member'),
    otherwise: (schema) => schema.notRequired()
  }),
  selectedGroupId: yup.string().when('postAs', {
    is: 'Group',
    then: (schema) => schema.required('Please select a group'),
    otherwise: (schema) => schema.notRequired()
  }),
  selectedGroupName: yup.string().when('postAs', {
    is: 'Group',
    then: (schema) => schema.required('Please select a group'),
    otherwise: (schema) => schema.notRequired()
  }),
  priority: yup
    .string()
    .required('Priority is required')
    .oneOf(['low', 'normal', 'high', 'urgent'], 'Invalid priority value'),
  label: yup.string().required('Label is required'),
  startDate: yup.string().required('Start date is required'),
  startTime: yup.string().required('Start time is required'),
  endDate: yup.string().required('End date is required'),
  endTime: yup.string().required('End time is required'),
  selectedTowers: yup.array().notRequired(),
  selectedUnits: yup.array().notRequired(),
  attachments: yup.array()
});

/**
 * CreateAnnouncement Component
 * Main form for creating announcements with real-time preview
 */
const CreateAnnouncement = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [currentUser, setCurrentUser] = useState(null);
  const [attachments, setAttachments] = useState([]);
  const [successMessage, setSuccessMessage] = useState('');
  const [fileUploadError, setFileUploadError] = useState('');
  const [apiError, setApiError] = useState('');
  const [towerError, setTowerError] = useState('');
  const [unitError, setUnitError] = useState('');
  const [formError, setFormError] = useState('');
  const [titleError, setTitleError] = useState('');
  const [descriptionError, setDescriptionError] = useState('');
  const [priorityError, setPriorityError] = useState('');
  const [labelError, setLabelError] = useState('');
  const [startDateError, setStartDateError] = useState('');
  const [startTimeError, setStartTimeError] = useState('');
  const [endDateError, setEndDateError] = useState('');
  const [endTimeError, setEndTimeError] = useState('');
  const [creatorNameError, setCreatorNameError] = useState('');
  const [postAsError, setPostAsError] = useState('');
  const [titleWordLimitError, setTitleWordLimitError] = useState('');

  // Get the source tab from location state (passed from AnnouncementList)
  const sourceTab = location.state?.sourceTab || null;

  // Get saved postAs preference from localStorage
  const getSavedPostAsPreference = () => {
    try {
      return localStorage.getItem('announcementPostAs') || '';
    } catch (error) {
      console.error('Error getting saved postAs preference:', error);
      return '';
    }
  };

  // Save postAs preference to localStorage
  const savePostAsPreference = (value) => {
    localStorage.setItem('announcementPostAs', value);
  };

  // Form setup with validation
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    getValues,
    formState: { errors, isSubmitting, isValid }
  } = useForm({
    resolver: yupResolver(announcementSchema),
    mode: 'onChange',
    defaultValues: {
      title: '',
      description: '',
      postAs: getSavedPostAsPreference(),
      creatorName: '',
      selectedMemberId: '',
      selectedMemberName: '',
      selectedGroupId: '',
      selectedGroupName: '',
      priority: 'normal',
      label: '',
      startDate: '',
      startTime: '',
      endDate: '',
      endTime: '',
      selectedTowers: [],
      selectedUnits: [],
      attachments: []
    }
  });

  // Handle title input change to limit words
  const handleTitleChange = (value, onChange) => {
    if (!value || value.trim() === '') {
      onChange('');
      setTitleWordLimitError('');
      return;
    }

    const words = value.trim().split(/\s+/);
    if (words.length <= 10) {
      onChange(value);
      setTitleWordLimitError('');
    } else {
      // Show error message when trying to exceed 10 words
      setTitleWordLimitError('Cannot write more than 10 words');
      // Don't update the input value - let user see what they typed but show error
      // The validation will prevent form submission
    }
  };

  // Get current word count for title
  const getTitleWordCount = (value) => {
    if (!value || value.trim() === '') return 0;
    return value.trim().split(/\s+/).length;
  };

  // Check if all required fields are filled
  const isFormValid = () => {
    const values = getValues();
    return (
      values.title &&
      values.creatorName &&
      values.priority &&
      values.label &&
      values.startDate &&
      values.startTime &&
      values.endDate &&
      values.endTime &&
      (values.postAs === 'Creator' ||
        (values.postAs === 'Group' && values.selectedGroupId) ||
        (values.postAs === 'Member' && values.selectedMemberId))
    );
  };

  // Watch all form values for real-time preview
  const watchedValues = watch();

  // Watch creator name for auto-sync
  const creatorName = watch('creatorName');

  // Watch selected towers for unit filtering
  const selectedTowers = watch('selectedTowers');

  // Clear tower/unit errors when selections change
  useEffect(() => {
    if (selectedTowers && selectedTowers.length > 0) {
      setTowerError('');
    }
  }, [selectedTowers]);

  const selectedUnits = watch('selectedUnits');
  useEffect(() => {
    if (selectedUnits && selectedUnits.length > 0) {
      setUnitError('');
    }
  }, [selectedUnits]);

  // Clear form errors when user starts typing
  const watchedFields = watch(['title', 'description', 'priority', 'label', 'startDate', 'startTime', 'endDate', 'endTime', 'creatorName', 'postAs']);
  useEffect(() => {
    setFormError('');
  }, [watchedFields]);

  // Clear specific field errors when user starts typing in those fields
  const title = watch('title');
  useEffect(() => {
    if (title) {
      setTitleError('');
      // Clear word limit error when user starts typing again
      const wordCount = getTitleWordCount(title);
      if (wordCount <= 10) {
        setTitleWordLimitError('');
      }
    }
  }, [title]);

  const description = watch('description');
  useEffect(() => {
    if (description) setDescriptionError('');
  }, [description]);

  const priority = watch('priority');
  useEffect(() => {
    if (priority) setPriorityError('');
  }, [priority]);

  const label = watch('label');
  useEffect(() => {
    if (label) setLabelError('');
  }, [label]);

  const startDate = watch('startDate');
  useEffect(() => {
    if (startDate) setStartDateError('');
  }, [startDate]);

  const startTime = watch('startTime');
  useEffect(() => {
    if (startTime) setStartTimeError('');
  }, [startTime]);

  const endDate = watch('endDate');
  useEffect(() => {
    if (endDate) setEndDateError('');
  }, [endDate]);

  const endTime = watch('endTime');
  useEffect(() => {
    if (endTime) setEndTimeError('');
  }, [endTime]);

  useEffect(() => {
    if (creatorName) setCreatorNameError('');
  }, [creatorName]);

  const postAs = watch('postAs');
  useEffect(() => {
    if (postAs) setPostAsError('');
  }, [postAs]);

  // Handle member selection
  const handleMemberSelect = (memberData) => {
    if (memberData) {
      setValue('selectedMemberId', memberData.id);
      setValue('selectedMemberName', memberData.name);
      // Set creator name to selected member when member is selected
      setValue('creatorName', memberData.name);
    } else {
      setValue('selectedMemberId', '');
      setValue('selectedMemberName', '');
      // Reset creator name to current user when member selection is cleared (like Group behavior)
      const user = currentUser || (() => {
        try {
          const member = localStorage.getItem('member');
          return member ? JSON.parse(member) : null;
        } catch (error) {
          return null;
        }
      })();
      if (user) {
        setValue('creatorName', user.full_name || user.fullName || 'Current User');
      }
    }
  };

  // Handle group selection
  const handleGroupSelect = (groupData) => {
    if (groupData) {
      setValue('selectedGroupId', groupData.id);
      setValue('selectedGroupName', groupData.name);
      // Set creator name to current user when group is selected
      if (currentUser) {
        setValue('creatorName', currentUser.full_name || currentUser.fullName || 'Current User');
      }
    } else {
      setValue('selectedGroupId', '');
      setValue('selectedGroupName', '');
    }
  };

  // Get current user info on component mount
  useEffect(() => {
    // Get user data from localStorage (set during login)
    const getCurrentUser = () => {
      try {
        const member = localStorage.getItem('member');
        return member ? JSON.parse(member) : null;
      } catch (error) {
        console.error('Error getting current user:', error);
        return null;
      }
    };

    const user = getCurrentUser();
    if (user) {
      setCurrentUser(user);
    }

    // Load saved post type preference
    const savedPostAs = localStorage.getItem('announcementPostAs');
    if (savedPostAs) {
      setValue('postAs', savedPostAs);
      // Set creator name to current user for all modes (Creator, Group, Member)
      if (user) {
        setValue('creatorName', user.full_name || user.fullName || 'Current User');
      }
    } else {
      // Default to Creator mode and set creator name
      setValue('postAs', 'Creator');
      if (user) {
        setValue('creatorName', user.full_name || user.fullName || 'Current User');
      }
    }
  }, [setValue]);

  // Utility function to convert file to base64
  const fileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  };

  // Handle file upload
  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);

    // Clear previous errors
    setFileUploadError('');

    // Check if adding these files would exceed the 5-file limit
    if (attachments.length + files.length > 5) {
      setFileUploadError('Maximum 5 files allowed. Please remove some files and try again.');
      // Reset the file input
      event.target.value = '';
      return;
    }

    // Check file types and sizes
    const validFiles = [];
    const invalidFiles = [];

    for (const file of files) {
      const isImage = file.type.startsWith('image/');
      const isPDF = file.type === 'application/pdf';
      const isDoc = file.type === 'application/msword' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

      if (!isImage && !isPDF && !isDoc) {
        invalidFiles.push(file.name);
        continue;
      }

      // Check file size: 5MB for all files
      if (file.size > 5 * 1024 * 1024) {
        setFileUploadError(`File "${file.name}" exceeds the 5MB size limit. Please choose a smaller file.`);
        // Reset the file input
        event.target.value = '';
        return;
      }

      validFiles.push(file);
    }

    if (invalidFiles.length > 0) {
      setFileUploadError(`Invalid file type(s): ${invalidFiles.join(', ')}. Only images (JPG, PNG), PDF, and DOC files are allowed.`);
      // Reset the file input
      event.target.value = '';
      return;
    }

    // Clear any previous error
    setFileUploadError('');

    try {
      const newAttachments = await Promise.all(
        validFiles.map(async (file) => {
          const base64 = await fileToBase64(file);
          return {
            id: Date.now() + Math.random(),
            file,
            url: base64, // Use base64 instead of blob URL
            base64: base64, // Store base64 for saving
            name: file.name,
            type: file.type
          };
        })
      );

      setAttachments(prev => [...prev, ...newAttachments]);
    } catch (error) {
      console.error('Error processing files:', error);
    }

    // Reset the file input to allow selecting the same file again
    event.target.value = '';
  };

  // Remove attachment
  const removeAttachment = (id) => {
    setAttachments(prev => prev.filter(att => att.id !== id));
  };

  // Utility function to determine announcement status based on dates
  const getAnnouncementStatus = (startDate, startTime, endDate, endTime) => {
    if (!startDate || !startTime || !endDate || !endTime) {
      return 'draft';
    }

    try {
      const now = new Date();

      // Handle different date formats and ensure proper parsing
      let startDateStr = startDate;
      let endDateStr = endDate;

      // If dates are in YYYY-MM-DD format, use them directly
      if (typeof startDate === 'string' && startDate.includes('-')) {
        startDateStr = startDate;
      } else {
        // Convert to YYYY-MM-DD format if needed
        const startDateObj = new Date(startDate);
        startDateStr = startDateObj.toISOString().split('T')[0];
      }

      if (typeof endDate === 'string' && endDate.includes('-')) {
        endDateStr = endDate;
      } else {
        // Convert to YYYY-MM-DD format if needed
        const endDateObj = new Date(endDate);
        endDateStr = endDateObj.toISOString().split('T')[0];
      }

      // Create datetime objects for comparison
      const startDateTime = new Date(`${startDateStr}T${startTime}`);
      const endDateTime = new Date(`${endDateStr}T${endTime}`);

      if (now < startDateTime) {
        return 'upcoming';
      } else if (now >= startDateTime && now <= endDateTime) {
        return 'ongoing';
      } else {
        return 'expired';
      }
    } catch (error) {
      console.warn('Error calculating announcement status:', error);
      return 'draft';
    }
  };

  // Word count utility functions
  const countWords = (text) => {
    if (!text || text.trim() === '') return 0;
    return text.trim().split(/\s+/).length;
  };

  const handleDescriptionKeyDown = (e, currentValue) => {
    const currentWordCount = countWords(currentValue);

    // If we're under the limit, allow all typing
    if (currentWordCount < 100) {
      return; // Allow the keystroke
    }

    // If we're at or over 100 words, block EVERYTHING except backspace and delete
    if (currentWordCount >= 100) {
      // Only allow backspace and delete to remove content
      if (e.key === 'Backspace' || e.key === 'Delete') {
        return; // Allow deletion
      }

      // Block everything else - no typing, no spaces, no navigation
      e.preventDefault();
      return;
    }
  };

  // Helper function to compress base64 image
  const compressBase64Image = (base64String) => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Calculate new dimensions (max 800px width/height)
        const maxDimension = 800;
        let { width, height } = img;

        if (width > height) {
          if (width > maxDimension) {
            height = (height * maxDimension) / width;
            width = maxDimension;
          }
        } else {
          if (height > maxDimension) {
            width = (width * maxDimension) / height;
            height = maxDimension;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height);
        const compressedBase64 = canvas.toDataURL('image/jpeg', 0.7); // 70% quality
        resolve(compressedBase64);
      };
      img.src = base64String;
    });
  };

  // Helper function to check localStorage space
  const getLocalStorageSize = () => {
    let total = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        total += localStorage[key].length + key.length;
      }
    }
    return total;
  };

  // Helper function to clean up old announcements if needed
  const cleanupOldAnnouncements = () => {
    try {
      const existingAnnouncements = JSON.parse(localStorage.getItem('announcements') || '[]');
      if (existingAnnouncements.length > 50) {
        // Keep only the 30 most recent announcements
        const sortedAnnouncements = existingAnnouncements
          .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
          .slice(0, 30);
        localStorage.setItem('announcements', JSON.stringify(sortedAnnouncements));
        console.log('Cleaned up old announcements, kept 30 most recent');
      }
    } catch (error) {
      console.warn('Error cleaning up announcements:', error);
    }
  };

  // Emergency function to clear all announcements (for development/debugging)
  const clearAllAnnouncements = () => {
    try {
      localStorage.removeItem('announcements');
      console.log('All announcements cleared from localStorage');
    } catch (error) {
      console.warn('Error clearing announcements:', error);
    }
  };

  // Add this to window for debugging purposes (can be called from browser console)
  useEffect(() => {
    window.clearAllAnnouncements = clearAllAnnouncements;
    window.getLocalStorageSize = getLocalStorageSize;
    return () => {
      delete window.clearAllAnnouncements;
      delete window.getLocalStorageSize;
    };
  }, []);

  // Helper function to clear all error messages
  const clearAllErrors = () => {
    setApiError('');
    setFileUploadError('');
    setTowerError('');
    setUnitError('');
    setFormError('');
    setTitleError('');
    setDescriptionError('');
    setPriorityError('');
    setLabelError('');
    setStartDateError('');
    setStartTimeError('');
    setEndDateError('');
    setEndTimeError('');
    setCreatorNameError('');
    setPostAsError('');
    setTitleWordLimitError('');
  };

  // Handle form submission
  const onSubmit = async (data) => {
    try {
      // Clear all previous errors
      clearAllErrors();
      setApiError('');
      setFormError('');

      // Validate form data
      let hasErrors = false;
      let errorMessages = [];

      if (!data.title) {
        errorMessages.push('Title is required.');
        hasErrors = true;
      }

      // Check title word limit
      if (data.title && getTitleWordCount(data.title) > 10) {
        errorMessages.push('Title cannot be more than 10 words.');
        hasErrors = true;
      }

      if (!data.creatorName) {
        errorMessages.push('Creator name is required.');
        hasErrors = true;
      }

      if (!data.priority) {
        errorMessages.push('Priority is required.');
        hasErrors = true;
      }

      if (!data.label) {
        errorMessages.push('Label is required.');
        hasErrors = true;
      }

      if (!data.startDate) {
        errorMessages.push('Start date is required.');
        hasErrors = true;
      }

      if (!data.startTime) {
        errorMessages.push('Start time is required.');
        hasErrors = true;
      }

      if (!data.endDate) {
        errorMessages.push('End date is required.');
        hasErrors = true;
      }

      if (!data.endTime) {
        errorMessages.push('End time is required.');
        hasErrors = true;
      }

      // Check post as validation
      if (data.postAs === 'Group' && !data.selectedGroupId) {
        errorMessages.push('Please select a group.');
        hasErrors = true;
      }

      if (data.postAs === 'Member' && !data.selectedMemberId) {
        errorMessages.push('Please select a member.');
        hasErrors = true;
      }

      if (!data.startDate) {
        setStartDateError('Start date is required.');
        hasErrors = true;
      }

      if (!data.startTime) {
        setStartTimeError('Start time is required.');
        hasErrors = true;
      }

      if (!data.endDate) {
        setEndDateError('End date is required.');
        hasErrors = true;
      }

      if (!data.endTime) {
        setEndTimeError('End time is required.');
        hasErrors = true;
      }

      // Validate post_as specific fields
      if (data.postAs === 'Group' && !data.selectedGroupId) {
        setPostAsError('Please select a group when posting as a group.');
        hasErrors = true;
      } else if (data.postAs === 'Member' && !data.selectedMemberId) {
        setPostAsError('Please select a member when posting as a member.');
        hasErrors = true;
      }

      // If there are validation errors, stop submission
      if (hasErrors) {
        setFormError(`Please fix the following errors:\n• ${errorMessages.join('\n• ')}`);
        return;
      }

      // Import the backend API
      const { announcementApi, formatAnnouncementForApi } = await import('../../../api/announcementsApi/announcementsBackendApi');

      try {
        // Format data for API
        const apiData = formatAnnouncementForApi(data, attachments);

        // Create announcement via API
        const createdAnnouncement = await announcementApi.createAnnouncement(apiData);

        // Clear all error states on success
        clearAllErrors();

        // Show success message
        setSuccessMessage('Announcement has been successfully created.');

      } catch (error) {
        console.error('Error creating announcement:', error);
        
        // Handle validation errors from formatAnnouncementForApi
        if (error.message && !error.response) {
          setApiError(error.message);
          return;
        }

        // Handle API errors
        if (error.response?.status === 400) {
          const errorDetails = error.response?.data?.details;
          if (errorDetails) {
            const fieldErrors = [];
            
            // Map backend field names to frontend field names
            const fieldMapping = {
              'title': 'title',
              'description': 'description',
              'priority': 'priority',
              'label': 'label',
              'start_date': 'startDate',
              'start_time': 'startTime',
              'end_date': 'endDate',
              'end_time': 'endTime',
              'post_as': 'postAs',
              'posted_group': 'selectedGroupId',
              'posted_member': 'selectedMemberId',
              'target_tower_ids': 'selectedTowers',
              'target_unit_ids': 'selectedUnits'
            };

            // Process each error field
            Object.entries(errorDetails).forEach(([field, messages]) => {
              const frontendField = fieldMapping[field] || field;
              const errorMessage = Array.isArray(messages) ? messages.join(', ') : messages;
              
              // Set specific field error if it exists in our mapping
              if (frontendField in fieldMapping) {
                const setterName = `set${frontendField.charAt(0).toUpperCase() + frontendField.slice(1)}Error`;
                if (typeof this[setterName] === 'function') {
                  this[setterName](errorMessage);
                }
              }
              
              fieldErrors.push(`${field}: ${errorMessage}`);
            });

            if (fieldErrors.length > 0) {
              setApiError(`Please fix the following errors:\n• ${fieldErrors.join('\n• ')}`);
            }
          } else {
            setApiError('Invalid data. Please check your inputs and try again.');
          }
        } else if (error.response?.status === 401) {
          setApiError('You are not authorized. Please log in again.');
        } else if (error.response?.status === 500) {
          setApiError('Server error. Please try again later.');
        } else if (error.message?.includes('Network Error')) {
          setApiError('Network error. Please check your connection and try again.');
        } else {
          setApiError('Failed to create announcement. Please try again.');
        }
      }
    } catch (error) {
      console.error('Unexpected error:', error);
      setApiError('An unexpected error occurred. Please try again.');
    }
  };

  // Handle form validation errors
  const onError = (errors) => {
    console.log('Form validation errors:', errors);

    // Clear all errors first
    clearAllErrors();

    // Set specific error messages for each field
    if (errors.creatorName) {
      setCreatorNameError('Creator name is required.');
    }

    if (errors.postAs) {
      setPostAsError('Please select how you want to post this announcement.');
    }

    if (errors.title) {
      setTitleError('Title is required.');
    }

    if (errors.description) {
      setDescriptionError('Description is required.');
    }

    if (errors.priority) {
      setPriorityError('Please select a priority level.');
    }

    if (errors.label) {
      setLabelError('Label is required.');
    }

    if (errors.startDate) {
      setStartDateError('Start date is required.');
    }

    if (errors.startTime) {
      setStartTimeError('Start time is required.');
    }

    if (errors.endDate) {
      setEndDateError('End date is required.');
    }

    if (errors.endTime) {
      setEndTimeError('End time is required.');
    }

    // Tower and unit selection is now optional
    // if (errors.selectedTowers) {
    //   setTowerError('Please select at least one tower.');
    // }

    // if (errors.selectedUnits) {
    //   setUnitError('Please select at least one unit.');
    // }

    // Set a general form error message
    const errorFields = Object.keys(errors);
    if (errorFields.length > 0) {
      setFormError('Please fill in all required fields correctly before submitting.');
    }
  };

  // Handle back navigation
  const handleBack = () => {
    // Navigate back to the same tab the user came from
    const targetTab = sourceTab || 1; // Default to ongoing tab if no source tab
    navigate('/announcements', {
      state: { activeTab: targetTab },
      replace: true
    });
  };

  // Clear success message
  const clearMessage = () => {
    setSuccessMessage('');
  };

  // Handle success message OK button
  const handleSuccessOk = () => {
    // Navigate back to announcements list with the correct tab
    const targetTab = sourceTab || 1; // Default to ongoing tab if no source tab
    navigate('/announcements', {
      state: { activeTab: targetTab },
      replace: true
    });
  };

  // Prepare data for preview component
  const previewData = {
    title: watchedValues.title,
    description: watchedValues.description,
    postAs: watchedValues.postAs,
    authorName: watchedValues.creatorName,
    selectedGroupName: watchedValues.selectedGroupName,
    selectedMemberName: watchedValues.selectedMemberName,
    priority: watchedValues.priority,
    label: watchedValues.label,
    startDate: watchedValues.startDate,
    startTime: watchedValues.startTime,
    endDate: watchedValues.endDate,
    endTime: watchedValues.endTime,
    attachments: attachments.map(att => ({
      preview: att.base64 || att.url, // Use base64 for preview
      name: att.name,
      type: att.type
    }))
  };

  return (
    <div className="min-h-screen bg-[#F5F5F5]">
      {/* Header */}
      <div className=" shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <button
              onClick={handleBack}
              className="flex items-center text-gray-600  transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              <span className="text-lg font-semibold">Create Announcements</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Left Column - Preview */}
          <div className="order-2 lg:order-1 lg:col-span-4">
            <div className="bg-white rounded-lg shadow-sm p-6 lg:sticky lg:top-8 h-screen lg:h-[calc(100vh-6rem)] overflow-y-auto">
              {/* <h3 className="text-lg font-semibold text-[#3D9D9B] mb-4">Preview</h3> */}
              <AnnouncementPreview data={previewData} currentUser={currentUser} />
            </div>
          </div>

          {/* Right Column - Form (wider) */}
          <div className="order-1 lg:order-2 lg:col-span-8">
            <form onSubmit={handleSubmit(onSubmit, onError)} className="space-y-6">
              {/* Announcement Author Section */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-[#3D9D9B] mb-4">Announcement Author</h3>

                {/* Creator Name and Post as on different rows */}
                <div className="space-y-4">
                  {/* Creator Name */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Creator Name
                    </label>
                    <Controller
                      name="creatorName"
                      control={control}
                      render={({ field }) => (
                        <input
                          {...field}
                          type="text"
                          readOnly
                          className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-[#999999] cursor-not-allowed"
                          value={currentUser?.full_name || currentUser?.fullName || 'Current User'}
                        />
                      )}
                    />
                  </div>

                  {/* Post as */}
                  <div>
                    <div className="flex items-center mb-3">
                      <label className="block text-sm font-semibold text-gray-700">
                        Post as <span className="text-[#3D9D9B]">*</span>
                      </label>
                      <div className="ml-8">
                        <Controller
                          name="postAs"
                          control={control}
                          render={({ field }) => (
                            <div className="flex space-x-6">
                              <label className="flex items-center cursor-pointer group">
                                <div className="relative">
                                  <input
                                    type="radio"
                                    {...field}
                                    value="Creator"
                                    checked={field.value === 'Creator'}
                                    onChange={(e) => {
                                      field.onChange(e.target.value);
                                      savePostAsPreference(e.target.value);
                                      // Clear member and group selections when switching to Creator
                                      setValue('selectedMemberId', '');
                                      setValue('selectedMemberName', '');
                                      setValue('selectedGroupId', '');
                                      setValue('selectedGroupName', '');
                                      // Set creator name to current user
                                      const user = currentUser || (() => {
                                        try {
                                          const member = localStorage.getItem('member');
                                          return member ? JSON.parse(member) : null;
                                        } catch (error) {
                                          return null;
                                        }
                                      })();
                                      if (user) {
                                        setValue('creatorName', user.full_name || user.fullName || 'Current User');
                                      }
                                    }}
                                    className="sr-only peer"
                                  />
                                  <div className="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-[#3D9D9B] peer-checked:border-4"></div>
                                </div>
                                <span className="ml-2 text-sm text-gray-700">Creator</span>
                              </label>
                              <label className="flex items-center cursor-pointer group">
                                <div className="relative">
                                  <input
                                    type="radio"
                                    {...field}
                                    value="Group"
                                    checked={field.value === 'Group'}
                                    onChange={(e) => {
                                      field.onChange(e.target.value);
                                      savePostAsPreference(e.target.value);
                                      // Clear member selection when switching to Group
                                      setValue('selectedMemberId', '');
                                      setValue('selectedMemberName', '');
                                      // Clear group selection to allow fresh selection
                                      setValue('selectedGroupId', '');
                                      setValue('selectedGroupName', '');
                                      // Set creator name to current user when switching to Group
                                      const user = currentUser || (() => {
                                        try {
                                          const member = localStorage.getItem('member');
                                          return member ? JSON.parse(member) : null;
                                        } catch (error) {
                                          return null;
                                        }
                                      })();
                                      if (user) {
                                        setValue('creatorName', user.full_name || user.fullName || 'Current User');
                                      }
                                    }}
                                    className="sr-only peer"
                                  />
                                  <div className="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-[#3D9D9B] peer-checked:border-4"></div>
                                </div>
                                <span className="ml-2 text-sm text-gray-700">Group</span>
                              </label>
                              <label className="flex items-center cursor-pointer group">
                                <div className="relative">
                                  <input
                                    type="radio"
                                    {...field}
                                    value="Member"
                                    checked={field.value === 'Member'}
                                    onChange={(e) => {
                                      field.onChange(e.target.value);
                                      savePostAsPreference(e.target.value);
                                      // Clear member and group selections when switching to Member
                                      setValue('selectedMemberId', '');
                                      setValue('selectedMemberName', '');
                                      setValue('selectedGroupId', '');
                                      setValue('selectedGroupName', '');
                                      // Set creator name to current user when switching to Member (like Group)
                                      const user = currentUser || (() => {
                                        try {
                                          const member = localStorage.getItem('member');
                                          return member ? JSON.parse(member) : null;
                                        } catch (error) {
                                          return null;
                                        }
                                      })();
                                      if (user) {
                                        setValue('creatorName', user.full_name || user.fullName || 'Current User');
                                      }
                                    }}
                                    className="sr-only peer"
                                  />
                                  <div className="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-[#3D9D9B] peer-checked:border-4"></div>
                                </div>
                                <span className="ml-2 text-sm text-gray-700">Member</span>
                              </label>
                            </div>
                          )}
                        />
                      </div>
                    </div>
                    {errors.postAs && (
                      <ErrorMessage message={postAsError} />
                    )}

                    {/* Show preview text box for Creator */}
                    {postAs === 'Creator' && (
                      <div className="mt-2">
                       
                        <input
                          type="text"
                          value={watch('creatorName')}
                          readOnly
                          className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-700"
                          placeholder="Preview of creator name"
                        />
                      </div>
                    )}
                  </div>

                  {/* Group Selector - Show when Group is selected */}
                  {postAs === 'Group' && (
                    <div>
                      <GroupSelector
                        value={watch('selectedGroupId')}
                        onChange={handleGroupSelect}
                        error={errors.selectedGroupId?.message}
                      />
                    </div>
                  )}

                  {/* Member Selector - Show when Member is selected */}
                  {postAs === 'Member' && (
                    <div>
                      <MemberSelector
                        value={watch('selectedMemberId')}
                        onChange={handleMemberSelect}
                        error={errors.selectedMemberId?.message}
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Announcement Information Section */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-[#3D9D9B] mb-4">Announcement Information</h3>

                {/* Title */}
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Title <span className="text-[#3D9D9B]">*</span>
                  </label>
                  <Controller
                    name="title"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <input
                          {...field}
                          type="text"
                          onChange={(e) => handleTitleChange(e.target.value, field.onChange)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-transparent"
                          placeholder="Announcement Title (max 10 words)"
                          value={field.value}
                        />
                        <div className="flex justify-between items-center mt-1">
                          <div>
                            <ErrorMessage message={titleError} />
                            {titleWordLimitError && (
                              <p className="text-sm text-red-600">{titleWordLimitError}</p>
                            )}
                          </div>
                          <p className={`text-xs ${getTitleWordCount(field.value) > 10 ? 'text-red-500' : 'text-gray-500'}`}>
                            
                          </p>
                        </div>
                      </div>
                    )}
                  />
                </div>

                {/* Description */}
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Description
                  </label>
                  <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                      <textarea
                        {...field}
                        rows={4}
                        onChange={(e) => {
                          const words = e.target.value.trim() === ''
                            ? []
                            : e.target.value.trim().split(/\s+/);
                          const limited = words.slice(0, 100).join(' ');
                          field.onChange(limited);
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-transparent"
                        placeholder="Write your description here... (max 100 words)"
                        value={field.value}
                      />
                    )}
                  />
                  {errors.description && (
                    <ErrorMessage message={descriptionError} />
                  )}
                </div>


                {/* Attachments */}
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Attachments
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                    <input
                      type="file"
                      multiple
                      accept="image/*,.pdf,.doc,.docx"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="file-upload"
                    />
                    <label
                      htmlFor="file-upload"
                      className="cursor-pointer flex flex-col items-center"
                    >
                      <Upload className="w-8 h-8 text-gray-400 mb-2" />
                      <span className="text-sm text-gray-600">Click to upload files</span>
                    </label>
                  </div>

                  {/* Error Message */}
                  <ErrorMessage message={fileUploadError} />

                  {/* Display uploaded files */}
                  {attachments.length > 0 && (
                    <div className="mt-3 grid grid-cols-3 gap-2">
                      {attachments.map((attachment) => (
                        <div key={attachment.id} className="relative">
                          {attachment.type.startsWith('image/') ? (
                            <img
                              src={attachment.url}
                              alt={attachment.name}
                              className="w-full h-20 object-cover rounded border"
                            />
                          ) : (
                            <div className="w-full h-20 bg-gray-100 rounded border flex items-center justify-center">
                              {attachment.type === 'application/pdf' ? (
                                <div className="flex flex-col items-center">
                                  <svg className="w-8 h-8 text-black font-bold" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                                  </svg>
                                  <span className="text-xs text-black font-bold mt-1">PDF</span>
                                </div>
                              ) : (
                                <div className="flex flex-col items-center">
                                  <svg className="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                                  </svg>
                                  <span className="text-xs text-gray-600 mt-1">DOC</span>
                                </div>
                              )}
                            </div>
                          )}
                          <button
                            type="button"
                            onClick={() => removeAttachment(attachment.id)}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Label and Priority Section */}

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                {/* Label */}
                <div>
                  <Controller
                    name="label"
                    control={control}
                    render={({ field }) => (
                      <LabelSelector
                        value={field.value}
                        onChange={field.onChange}
                      />
                    )}
                  />
                  <ErrorMessage message={labelError} />
                </div>


                  {/* Priority */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Priority <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="priority"
                      control={control}
                      render={({ field }) => (
                        <PriorityDropdown
                          value={field.value}
                          onChange={field.onChange}
                          
                        />
                      )}
                    />
                    <ErrorMessage message={priorityError} />
                  </div>

                </div>
              </div>


              {/* Announcement Visibility Section */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-[#3D9D9B] mb-4">Announcement Visibility</h3>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {/* Start Date */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Start Date <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="startDate"
                      control={control}
                      render={({ field }) => (
                        <Calendar
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select Start Date"

                        />
                      )}
                    />
                    {errors.startDate && (
                      <ErrorMessage message={startDateError} />
                    )}
                    
                  </div>

                  {/* Start Time */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Start Time <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="startTime"
                      control={control}
                      render={({ field }) => (
                        <TimePicker
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select Start Time"
                        />
                      )}
                    />
                    {errors.startTime && (
                      <ErrorMessage message={startTimeError} />
                    )}
                    
                  </div>

                  {/* End Date */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      End Date <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="endDate"
                      control={control}
                      render={({ field }) => (
                        <Calendar
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select End Date"
                        />
                      )}
                    />
                    {errors.endDate && (
                      <ErrorMessage message={endDateError} />
                    )}
                    
                  </div>

                  {/* End Time */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      End Time <span className="text-[#3D9D9B]">*</span>
                    </label>
                    <Controller
                      name="endTime"
                      control={control}
                      render={({ field }) => (
                        <TimePicker
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select End Time"
                        />
                      )}
                    />
                    {errors.endTime && (
                     <ErrorMessage message={endTimeError} />
                    )}
                    
                  </div>
                </div>
              </div>

              {/* Tower and Unit Section */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Tower */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Tower
                    </label>
                    <Controller
                      name="selectedTowers"
                      control={control}
                      render={({ field }) => (
                        <TowerSelector
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select Towers"
                        />
                      )}
                    />
                    {errors.selectedTowers && (
                      <ErrorMessage message={towerError} />
                    )}
                    
                  </div>

                  {/* Unit */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Unit
                    </label>
                    <Controller
                      name="selectedUnits"
                      control={control}
                      render={({ field }) => (
                        <UnitSelector
                          value={field.value}
                          onChange={field.onChange}
                          selectedTowers={selectedTowers}
                          placeholder="Select Units"
                        />
                      )}
                    />
                    {errors.selectedUnits && (
                     <ErrorMessage message={unitError} />
                    )}
                    
                  </div>
                </div>
              </div>

              {/* Form Error Message */}
              <ErrorMessage message={formError} />

              {/* API Error Message */}
              <ErrorMessage message={apiError} />

              {/* Submit Button */}
              <div className="flex justify-center">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full px-8 py-3 rounded-md transition duration-200 font-medium ${
                    isFormValid() && !isSubmitting
                      ? 'bg-[#3D9D9B] text-white hover:bg-[#34877A]'
                      : 'bg-white text-[#3D9D9B] border-2 border-[#3D9D9B] hover:bg-gray-50'
                  } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  {isSubmitting ? 'Creating...' : 'Send'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Success Message Box */}
      <MessageBox
        message={successMessage}
        clearMessage={clearMessage}
        onOk={handleSuccessOk}
      />
    </div>
  );
};

export default CreateAnnouncement;
